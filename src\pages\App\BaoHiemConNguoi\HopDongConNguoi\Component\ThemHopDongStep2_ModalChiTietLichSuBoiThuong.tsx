import {ArrowLeftOutlined, CheckOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, HeaderModal} from "@src/components";
import {formatDateTimeToNumber} from "@src/utils";
import {Col, Flex, Form, message, Modal, Row} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useState} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {ChiTietLichSuBoiThuongProps, FormChiTietLichSuBoiThuong, IModalChiTietLichSuBoiThuongRef} from "./Constant";

const {so_hs, ma_sp, lh_nv, tien_bt, tien_bt_vat, tong_tien_bt, ngay_mo_hs, ngay_duyet_bt, tien_dp, ma_doi_tac_ql} = FormChiTietLichSuBoiThuong;

const ModalChiTietLichSuBoiThuongComponent = forwardRef<IModalChiTietLichSuBoiThuongRef, ChiTietLichSuBoiThuongProps>(
  ({chiTietHopDong, listNguoiPhuThuoc, initListNguoiPhuThuoc}: ChiTietLichSuBoiThuongProps, ref) => {
    useImperativeHandle(ref, () => ({
      open: (dataLichSuBoiThuong?: any) => {
        setIsOpen(true);
        if (dataLichSuBoiThuong) setChiTietLichSuBoiThuong(dataLichSuBoiThuong); // nếu có dữ liệu -> set chi tiết đối tác -> là sửa
      },
      close: () => setIsOpen(false),
    }));
    const [chiTietLichSuBoiThuong, setChiTietLichSuBoiThuong] = useState<CommonExecute.Execute.ILichSuBoiThuongHopDong | null>(null);
    const [isOpen, setIsOpen] = useState(false);

    const {chiTietNguoiDuocBaoHiem, loadingQuyenLoi, listQuyenLoiNguoiDuocBaoHiem, capNhatLichSuBoiThuong, getListSanPham, lietKeDieuKhoanNguoiDuocBaoHiem, listSanPham, listDoiTac} =
      useHopDongConNguoiContext();

    const [formChiTietLichSuBoiThuong] = Form.useForm();
    const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
    const formValues = Form.useWatch([], formChiTietLichSuBoiThuong);
    // init form data - load dữ liệu vào form khi sửa
    useEffect(() => {
      if (chiTietLichSuBoiThuong) {
        const arrFormData = [];
        for (const key in chiTietLichSuBoiThuong) {
          let value: any = chiTietLichSuBoiThuong[key as keyof CommonExecute.Execute.ILichSuBoiThuongHopDong];
          if (key === "ngay_sinh") value = dayjs(value + "", "YYYYMMDD").isValid() ? dayjs(value + "", "YYYYMMDD") : "";
          arrFormData.push({
            name: key,
            value,
          });
        }
        formChiTietLichSuBoiThuong.setFields(arrFormData);
      }
    }, [chiTietLichSuBoiThuong]);
    //lietKeDieuKhoanNguoiDuocBaoHiem
    useEffect(() => {
      console.log("chiTietNguoiDuocBaoHiem", chiTietNguoiDuocBaoHiem);
      if (chiTietNguoiDuocBaoHiem?.so_id && chiTietNguoiDuocBaoHiem?.so_id_dt) {
        lietKeDieuKhoanNguoiDuocBaoHiem({
          so_id: chiTietNguoiDuocBaoHiem?.so_id,
          so_id_dt: +(chiTietNguoiDuocBaoHiem?.so_id_dt || 0),
        });
      }
    }, [chiTietNguoiDuocBaoHiem]);
    // Tự động tính tổng tiền bồi thường khi tien_bt hoặc tien_bt_vat thay đổi
    useEffect(() => {
      const tienBt = formValues?.tien_bt || 0;
      const tienBtVat = formValues?.tien_bt_vat || 0;
      const tongTienBt = tienBt + tienBtVat;

      // Chỉ cập nhật nếu giá trị khác với giá trị hiện tại để tránh vòng lặp vô hạn
      if (formValues?.tong_tien_bt !== tongTienBt) {
        formChiTietLichSuBoiThuong.setFieldValue("tong_tien_bt", tongTienBt);
      }
    }, [formValues?.tien_bt, formValues?.tien_bt_vat, formValues?.tong_tien_bt, formChiTietLichSuBoiThuong]);

    //xử lý validate form
    useEffect(() => {
      formChiTietLichSuBoiThuong
        .validateFields({validateOnly: true}) // validateOnly : Chỉ xác thực nội dung và không hiển thị thông báo lỗi trên UI.
        .then(() => {
          setDisableSubmit(false); // nếu k có lỗi -> cho enable nút Lưu
        })
        .catch(() => {
          setDisableSubmit(true); // nếu có lỗi -> cho disable nút Lưu
        });
    }, [formChiTietLichSuBoiThuong, formValues]);

    const closeModal = useCallback(() => {
      setIsOpen(false);
      setChiTietLichSuBoiThuong(null);
      formChiTietLichSuBoiThuong.resetFields();
    }, [formChiTietLichSuBoiThuong]);

    // Change đối tác
    const onChangeDoiTac = (value: any) => {
      getListSanPham({ma_doi_tac_ql: value, nv: "NG"});
    };
    //Bấm Update
    const onClickLuuLichSuBoiThuong = async () => {
      try {
        console.log("formValues", formValues);
        const values: ReactQuery.ICapNhatLichSuBoiThuongHopDongParams = formChiTietLichSuBoiThuong.getFieldsValue(); //lấy ra values của form

        values.so_id = Number(chiTietNguoiDuocBaoHiem?.so_id);
        values.so_id_dt = chiTietNguoiDuocBaoHiem ? +(chiTietNguoiDuocBaoHiem.so_id_dt || -1) : -1;
        values.nv = "NG";
        values.so_hs = chiTietLichSuBoiThuong?.so_hs;
        values.tien_dp = values.tien_dp || 0;
        // values.stt = chiTietLichSuBoiThuong?.stt || (listNguoiPhuThuoc?.length || 0) + 1;
        values.tong_tien_bt = values.tien_bt + values.tien_bt_vat;
        values.tien_bt = values.tien_bt || 0;
        values.tien_bt_vat = values.tien_bt_vat || 0;
        values.ngay_duyet_bt = formatDateTimeToNumber(values.ngay_duyet_bt);
        values.ngay_mo_hs = formatDateTimeToNumber(values.ngay_mo_hs);

        const response = await capNhatLichSuBoiThuong(values); //cập nhật lại đối tác
        if (response) {
          message.success(`${chiTietLichSuBoiThuong ? "Cập nhật" : "Thêm mới"} thành công`);
          // initListNguoiPhuThuoc();
          // closeModal();
        }
      } catch (error) {
        console.log("onConfirm", error);
      }
    };

    // RENDER
    //FOOTER
    const renderFooter = () => {
      return (
        <div>
          <Button type="default" onClick={() => closeModal()} className="mr-2" icon={<ArrowLeftOutlined />}>
            Quay lại
          </Button>
          <Button disabled={disableSubmit} loading={loadingQuyenLoi} htmlType="submit" onClick={onClickLuuLichSuBoiThuong} icon={<CheckOutlined />}>
            Lưu
          </Button>
        </div>
      );
    };
    const renderFormColum = (props: IFormInput, span = 6) => (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
    const renderForm = () => (
      <Form form={formChiTietLichSuBoiThuong} layout="vertical">
        {/* MÃ */}
        <Row gutter={16}>
          {renderFormColum({...so_hs})}
          {renderFormColum({...ma_doi_tac_ql, options: listDoiTac, onChange: onChangeDoiTac})}
          {renderFormColum({...ma_sp, options: listSanPham})}
          {renderFormColum({...lh_nv, options: listQuyenLoiNguoiDuocBaoHiem})}
          {renderFormColum({...ngay_mo_hs})}
          {renderFormColum({...tien_dp})}
          {renderFormColum({...tien_bt})}
          {renderFormColum({...tien_bt_vat})}
          {renderFormColum({...tong_tien_bt})}
          {renderFormColum({...ngay_duyet_bt})}
        </Row>
      </Form>
    );
    //Render
    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          title={
            <HeaderModal
              title={chiTietLichSuBoiThuong ? `${chiTietLichSuBoiThuong.ten}` : "Tạo mới lịch sử bồi thường"}
              // trang_thai_ten={chiTietNguoiPhuThuoc?.trang_thai_ten}
              // trang_thai={chiTietNguoiPhuThuoc?.trang_thai}
            />
          }
          destroyOnClose
          // centered
          maskClosable={false}
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={"70%"}
          styles={{
            body: {
              //   height: "20vh",
              // overflowY: "auto",
              // overflowX: "hidden",
            },
          }}
          footer={renderFooter}>
          {renderForm()}
        </Modal>
      </Flex>
    );
  },
);

ModalChiTietLichSuBoiThuongComponent.displayName = "ModalChiTietNguoiPhuThuocComponent";
export const ModalChiTietLichSuBoiThuong = memo(ModalChiTietLichSuBoiThuongComponent, isEqual);
