import {forwardRef, memo, useCallback, useEffect, useImper<PERSON><PERSON>andle, useMemo, useRef, useState} from "react";
import {
  ngayApDungColumns,
  //   cauHoiColumns,
  //   DataIndexCauHoi,
  //   DataIndexNgayApDung,
  //   FormThemNgayApDung,
  IModalChiTietNganSachHoTroRef,
  Props,
  TableNgayApDungDataType,
  FormThemNgayApDung,
  DataIndexNgayApDung,
  FormChiTietNganSachHoTro,
  IModalThemNganSachHoTroNgayADRef,
  //   TableCauHoiDataType,
} from "./index.configs";
import {Col, Flex, Form, InputRef, Modal, Row, Table, TableColumnType} from "antd";
import {useCauHinhNganSachHoTroContext} from "../index.context";
import {isEqual} from "lodash";
import {Button, FormInput, HeaderModal, <PERSON><PERSON>er, <PERSON>comfirm, TableFilterDropdown} from "@src/components";
import {CheckOutlined, CloseOutlined, PlusCircleOutlined} from "@ant-design/icons";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {FilterDropdownProps} from "antd/es/table/interface";
import dayjs from "dayjs";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import {ReactQuery} from "@src/@types";
import {ModalThemNganSachHoTroNgayAD} from "./ModalThemNganSachHoTroNgayAD";
// import {ModalThemCauHoi} from "./ModalThemCauHoi";

dayjs.extend(isSameOrBefore);
// const {ma_doi_tac_ql, ten, nv} = FormchiTietTinhThanh;

const ModalChiTietNganSachHoTroComponent = forwardRef<IModalChiTietNganSachHoTroRef, Props>(({}: Props, ref) => {
  useImperativeHandle(ref, () => ({
    open: (dataDanhMucTinhThanh?: CommonExecute.Execute.IDanhMucTinhThanh) => {
      setIsOpen(true);
      if (!dataDanhMucTinhThanh) {
        setDanhSachNganSachHoTroNgayApDung([]);
        setChiTietNganSachHoTro(null);
      }
    },
    close: () => {
      setIsOpen(false);
    },
  }));
  const refModalThemNganSachHoTroNgayAD = useRef<IModalThemNganSachHoTroNgayADRef>(null);
  //   const refModalThemCauHoi = useRef<IModalThemCauHoiRef>(null);
  const {ngay_ad, ma_dvi, loai_ho_gia_dinh} = FormThemNgayApDung;
  const {ty_le_nsnn, ty_le_nsdp, ma_sp} = FormChiTietNganSachHoTro;
  const [isOpen, setIsOpen] = useState(false);
  const [pageSize, setPageSize] = useState(8);
  const {
    loading,
    filterParams,
    danhSachNganSachHoTroNgayApDung,
    chiTietTinhThanh,
    chiTietNganSachHoTro,
    listDonViThuHo,
    listLoaiHoGiaDinh,
    ngayAdMoiTao,
    setDanhSachNganSachHoTroNgayApDung,
    setChiTietNganSachHoTro,
    setNgayAdMoiTao,
    layDanhSachNganSachHoTroNgayApDung,
    layChiTietNganSachHoTro,
    CapNhatNganSachHoTro,
    xoaNgayApDungNganSachHoTro,
  } = useCauHinhNganSachHoTroContext();
  const [searchedColumn, setSearchedColumn] = useState("");
  const [searchText, setSearchText] = useState("");
  const searchInput = useRef<InputRef>(null);
  const [formBHXH] = Form.useForm();
  const [formBHYT] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //select ngày áp dụng bảo gồm ngày áp dụng và mã đơn vị
  const [selectedNgayApDung, setSelectedNgayApDung] = useState<{ngay_ad: string | null; ma_dvi: string | null; loai_ho_gia_dinh: string | null; bt: number | null} | null>(null);

  useEffect(() => {
    console.log("chiTietNganSachHoTro:", chiTietNganSachHoTro);

    if (chiTietNganSachHoTro) {
      let bhxhData = {};
      let bhytData = {};

      // Kiểm tra nếu chiTietNganSachHoTro là mảng
      if (Array.isArray(chiTietNganSachHoTro)) {
        // Tìm dữ liệu theo ma_sp
        bhxhData = chiTietNganSachHoTro.find(item => item.ma_sp === "BHXH") || {};
        bhytData = chiTietNganSachHoTro.find(item => item.ma_sp === "BHYT") || {};
      } else if (typeof chiTietNganSachHoTro === "object") {
        // Nếu là object, kiểm tra xem có phải là dữ liệu đơn lẻ không
        if (chiTietNganSachHoTro.ma_sp === "BHXH") {
          bhxhData = chiTietNganSachHoTro;
        } else if (chiTietNganSachHoTro.ma_sp === "BHYT") {
          bhytData = chiTietNganSachHoTro;
        }
      }
      formBHXH.setFieldsValue({
        ty_le_nsnn: (bhxhData as any).ty_le_nsnn,
        ty_le_nsdp: (bhxhData as any).ty_le_nsdp,
      });

      formBHYT.setFieldsValue({
        ty_le_nsnn: (bhytData as any).ty_le_nsnn,
        ty_le_nsdp: (bhytData as any).ty_le_nsdp,
      });
    }
  }, [chiTietNganSachHoTro, formBHXH, formBHYT]);
  useEffect(() => {
    // Reset form khi selectedNgayApDung thay đổi
    formBHXH.resetFields();
    formBHYT.resetFields();
  }, [selectedNgayApDung, formBHXH, formBHYT]);
  useEffect(() => {
    if (chiTietTinhThanh?.ma) {
      layDanhSachNganSachHoTroNgayApDung({ma_tinh: chiTietTinhThanh.ma, ngay_ad: chiTietTinhThanh.ngay_ad});
    }
  }, [chiTietTinhThanh]);

  useEffect(() => {
    if (selectedNgayApDung !== null) {
      setDisableSubmit(false);
    } else {
      setDisableSubmit(true);
    }
  }, [selectedNgayApDung]);
  // Tự động chọn dòng cuối cùng khi danh sách thay đổi
  useEffect(() => {
    if (danhSachNganSachHoTroNgayApDung.length > 0) {
      let selected;
      const selectedMaDvi = danhSachNganSachHoTroNgayApDung.find(item => item.ma_dvi === item.ma_dvi);
      if (ngayAdMoiTao) {
        // Tìm ngày vừa tạo
        selected = danhSachNganSachHoTroNgayApDung.find(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(dayjs(ngayAdMoiTao, "DD/MM/YYYY")));

        setNgayAdMoiTao(null); // Reset lại sau khi đã select
      } else {
        // Tìm ngày gần hôm nay nhất (không vượt quá hôm nay)
        const today = dayjs();
        const validDates = danhSachNganSachHoTroNgayApDung.filter(item => dayjs(item.ngay_ad, "DD/MM/YYYY").isBefore(today) || dayjs(item.ngay_ad, "DD/MM/YYYY").isSame(today));

        if (validDates.length > 0) {
          selected = validDates.reduce((prev, curr) => {
            const prevDiff = today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day");
            const currDiff = today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day");
            return currDiff < prevDiff ? curr : prev;
          });
        } else {
          // Fallback: nếu không có ngày nào <= hôm nay, chọn ngày gần nhất
          selected = danhSachNganSachHoTroNgayApDung.reduce((prev, curr) => {
            const prevDiff = Math.abs(today.diff(dayjs(prev.ngay_ad, "DD/MM/YYYY"), "day"));
            const currDiff = Math.abs(today.diff(dayjs(curr.ngay_ad, "DD/MM/YYYY"), "day"));
            return currDiff < prevDiff ? curr : prev;
          });
        }
      }
      if (selected && selected.ngay_ad !== undefined && selected.ngay_ad !== null) {
        setSelectedNgayApDung({
          ngay_ad: String(selected.ngay_ad),
          ma_dvi: String(selectedMaDvi),
          loai_ho_gia_dinh: String(selected.loai_ho_gia_dinh),
          bt: selected.bt,
        });
        console.log("selected.ngay_ad", selected.ngay_ad);
        layChiTietNganSachHoTro({bt_ad: selected.bt});
      }
    } else {
      setSelectedNgayApDung({ngay_ad: null, ma_dvi: null, loai_ho_gia_dinh: null, bt: null});
    }
  }, [danhSachNganSachHoTroNgayApDung, chiTietTinhThanh]);
  const closeModal = () => {
    setIsOpen(false);
    // setchiTietTinhThanh(null);
    // setDanhSachCauHoi([]);

    // form.resetFields();
    formBHXH.resetFields();
    formBHYT.resetFields();
    // setFilterParams(filterParams);
  };
  // Dữ liệu bảng câu hỏi áp dụng(bên trái)
  const dataTableListNgayApDung = useMemo<Array<TableNgayApDungDataType>>(() => {
    try {
      const mappedData = danhSachNganSachHoTroNgayApDung.map((item: any, index: number) => ({
        stt: item.sott ?? index + 1,
        bt: item.bt,
        ngay_ad: item.ngay_ad || "",
        ma_tinh: item.ma_tinh,
        ma_dvi: item.ma_dvi,
        loai_ho_gia_dinh: item.loai_ho_gia_dinh,
        key: index.toString(),
        hanh_dong: () => renderDeleteButton(item.bt),
      }));
      const arrEmptyRow = fillRowTableEmpty(mappedData.length, pageSize);
      return [...mappedData, ...arrEmptyRow];
    } catch (error) {
      console.error("Lỗi khi xử lý dữ liệu bảng ngày áp dụng:", error);
      return [];
    }
  }, [danhSachNganSachHoTroNgayApDung, pageSize, chiTietTinhThanh]);

  const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexNgayApDung) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  }, []);

  const handleReset = useCallback(
    (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: DataIndexNgayApDung) => {
      clearFilters();
      setSearchedColumn("");
      handleSearch([""], confirm, dataIndex);
    },
    [handleSearch],
  );
  const handleNgayApDungRowClick = async (record: TableNgayApDungDataType) => {
    // Nếu là hàng trống (key chứa "empty" hoặc không có bt), set null
    if (record.key?.toString().includes("empty") || record.bt === undefined || record.bt === null) {
      setSelectedNgayApDung({ngay_ad: null, ma_dvi: null, loai_ho_gia_dinh: null, bt: null});
      return;
    }

    console.log("record", record);
    setSelectedNgayApDung({ngay_ad: record.ngay_ad, ma_dvi: record.ma_dvi, loai_ho_gia_dinh: record.loai_ho_gia_dinh, bt: record.bt});
    layChiTietNganSachHoTro({bt_ad: record.bt});
  };

  const handleDelete = async (bt: number) => {
    try {
      // TODO: Implement delete function - onDeleteCauHoiApDung is not available
      console.log("Delete bt:", bt);
      await xoaNgayApDungNganSachHoTro({
        bt: Number(bt),
      });

      // Reset selection sau khi xóa
      setSelectedNgayApDung(null);
      //  setDanhSachCauHinhPhanCapPheDuyetCT([]);

      // Refresh danh sách
      if (chiTietTinhThanh?.ma) {
        layDanhSachNganSachHoTroNgayApDung({ma_tinh: chiTietTinhThanh.ma, ngay_ad: chiTietTinhThanh.ngay_ad});
      }
    } catch (error) {
      console.log("Lỗi khi xóa ngày áp dụng:", error);
    }
  };

  const onPressLuuNganSachHoTro = async () => {
    try {
      const valuesBHXH = await formBHXH.validateFields().catch(() => null);
      const valuesBHYT = await formBHYT.validateFields().catch(() => null);
      console.log("valuesBHXH", valuesBHXH);
      console.log("valuesBHYT", valuesBHYT);
      const ns_ct: any[] = [];

      if (valuesBHXH.ty_le_nsnn && valuesBHXH.ty_le_nsdp) {
        ns_ct.push({
          ma_sp: "BHXH",
          ty_le_nsnn: valuesBHXH.ty_le_nsnn,
          ty_le_nsdp: valuesBHXH.ty_le_nsdp,
        });
      }

      if (valuesBHYT && valuesBHYT.ty_le_nsnn && valuesBHYT.ty_le_nsdp) {
        ns_ct.push({
          ma_sp: "BHYT",
          ty_le_nsnn: valuesBHYT.ty_le_nsnn,
          ty_le_nsdp: valuesBHYT.ty_le_nsdp,
        });
      }

      const params: ReactQuery.ICapNhatNganSachHoTroParams = {
        // ma_tinh: chiTietTinhThanh?.ma,
        // ma_dvi: String(selectedNgayApDung?.ma_dvi),
        // ngay_ad: selectedNgayApDung ? Number(dayjs(selectedNgayApDung.ngay_ad, "DD/MM/YYYY").format("YYYYMMDD")) : undefined,
        bt: chiTietNganSachHoTro?.bt || 0,
        bt_ad: selectedNgayApDung?.bt,
        ns_ct,
      };

      await CapNhatNganSachHoTro(params);
      console.log("Lưu thành công:", params);
    } catch (error) {
      console.log("Lỗi khi submit:", error);
    }
  };

  const getColumnSearchCauHoiApDungProps = (dataIndex: DataIndexNgayApDung, title: string): TableColumnType<DataIndexNgayApDung> => ({
    filterDropdown: ({setSelectedKeys, selectedKeys, confirm, clearFilters}) => (
      <TableFilterDropdown
        ref={searchInput}
        title={title}
        selectedKeys={selectedKeys}
        dataIndex={dataIndex}
        setSelectedKeys={setSelectedKeys}
        handleSearch={handleSearch}
        confirm={confirm}
        clearFilters={clearFilters}
        handleReset={handleReset}
      />
    ),
    // filterIcon: (filtered: boolean) => <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} />,
    onFilter: (value, record) =>
      record[dataIndex as keyof DataIndexNgayApDung]
        ? record[dataIndex as keyof DataIndexNgayApDung]
            .toString()
            .toLowerCase()
            .includes((value as string).toLowerCase())
        : false,
    filterDropdownProps: {
      onOpenChange(open) {
        if (open) {
          setTimeout(() => searchInput.current?.select(), 100);
        }
      },
    },
    // filters: dataIndex === "trang_thai_ten" ? radioItemTrangThaiNhomTable : undefined,
    render: (text, record, index) => {
      //   if (dataIndex === "trang_thai_ten") {
      //     // Xác định màu dựa vào text (trạng thái hiển thị)
      //     const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
      //     if (record.key.toString().includes("empty")) return "";
      //     return <Tag color={color}>{text}</Tag>;
      //   }
      // if (record.key.toString().includes("empty")) return <div style={{height: 20}} />;

      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchText]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Button type="link" className="h-auto p-0" style={{color: "transparent"}} icon={<CloseOutlined style={{visibility: "hidden"}} />}></Button>
      );
    },
  });

  const renderDeleteButton = (bt?: number) => {
    if (!bt) return null;
    return (
      <div>
        <Popcomfirm
          title="Thông báo"
          onConfirm={() => handleDelete(bt)}
          htmlType="button"
          okText="Xóa"
          description="Bạn có chắc muốn xóa ngày áp dụng?"
          buttonTitle={""}
          buttonColor="red"
          okButtonProps={{
            style: {
              backgroundColor: "white",
              borderColor: "red",
              color: "red",
            },
          }}
          style={{width: "fit-content"}}
          variant="text"
          className="h-auto"
          icon={<CloseOutlined />}
          buttonIcon
        />
      </div>
    );
  };

  const renderTableCauHoiApDungFooter = () => {
    return (
      <div className="">
        <Form.Item className="" style={{marginTop: 16, marginRight: 8, marginBottom: 0}}>
          <Button
            className="w-full"
            type="primary"
            icon={<PlusCircleOutlined />}
            onClick={() => {
              refModalThemNganSachHoTroNgayAD.current?.open();
            }}>
            Thêm ngày áp dụng
          </Button>
        </Form.Item>
      </div>
    );
  };

  const renderTableCauHoiApDung = () => {
    return (
      <Table<TableNgayApDungDataType>
        className="table-vai-tro no-header-border-radius"
        {...defaultTableProps}
        style={{cursor: "pointer", borderBottom: "1px solid #f0f0f0"}}
        onRow={(record, rowIndex) => {
          return {
            style: {
              cursor: "pointer",
              background: record.bt === selectedNgayApDung?.bt ? "#96BF49" : undefined, // Làm nổi bật hàng được chọn
            },
            onClick: () => handleNgayApDungRowClick(record), // Xử lý nhấp vào hàng
          };
        }}
        title={null}
        pagination={false}
        columns={(ngayApDungColumns || []).map(item => {
          // Đảm bảo item.key được định nghĩa, item.title là chuỗi và item.key không phải là "stt" trước khi thêm ô tìm kiếm
          return {
            ...item,
            ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchCauHoiApDungProps(item.key as keyof TableNgayApDungDataType, item.title) : {}),
          };
        })}
        dataSource={dataTableListNgayApDung}
        bordered
        scroll={dataTableListNgayApDung.length > pageSize ? {y: 215} : undefined}
      />
    );
  };
  const renderFormInputColum = (props?: any, span = 4) => (
    <Col span={span}>
      <FormInput {...props} />
    </Col>
  );
  const renderForm = () => {
    return (
      <>
        {renderFormBHXH()}
        {renderFormBHYT()}
      </>
    );
  };
  const renderFormBHXH = () => {
    return (
      <div className="section-wrapper">
        <div className="section-title">Bảo hiểm xã hội tự nguyện</div>
        <Form form={formBHXH} layout="vertical" style={{padding: "16px 16px"}}>
          <Row gutter={16}>
            {renderFormInputColum({...ty_le_nsnn}, 12)}
            {renderFormInputColum({...ty_le_nsdp}, 12)}
          </Row>
        </Form>
      </div>
    );
  };

  const renderFormBHYT = () => {
    return (
      <div className="section-wrapper">
        <div className="section-title">Bảo hiểm y tế</div>
        <Form form={formBHYT} layout="vertical" style={{padding: "16px 16px"}}>
          <Row gutter={16}>
            {renderFormInputColum({...ty_le_nsnn}, 12)}
            {renderFormInputColum({...ty_le_nsdp}, 12)}
          </Row>
        </Form>
      </div>
    );
  };
  const renderFooter = () => {
    return (
      <Form.Item className="mb-0" style={{display: "flex", justifyContent: "end"}}>
        <Button type="default" htmlType="reset" form="formChiTietNganSachHoTro" onClick={() => closeModal()} className="mr-2" icon={<CloseOutlined />}>
          Đóng
        </Button>
        <Popcomfirm
          title="Thông báo"
          onConfirm={onPressLuuNganSachHoTro}
          htmlType="submit"
          okText="Lưu"
          description="Bạn có chắc chắn muốn lưu thông tin?"
          buttonTitle={"Lưu"}
          buttonDisable={disableSubmit}
          buttonIcon={<CheckOutlined />}
          iconPosition="end"
          loading={loading}
        />
      </Form.Item>
    );
  };
  const renderTable = () => {
    return (
      <Row>
        <Col span={9} style={{paddingRight: 16}}>
          {renderTableCauHoiApDung()}
          {renderTableCauHoiApDungFooter()}
        </Col>
        <Col span={15} style={{justifyContent: "space-between", display: "flex", flexDirection: "column"}}>
          {renderForm()}
          {renderFooter()}
        </Col>
      </Row>
    );
  };
  return (
    <Flex vertical gap="middle" align="flex-start">
      <Modal
        title={<HeaderModal title={chiTietTinhThanh ? `Chi tiết ngân sách hỗ trợ của ${chiTietTinhThanh.ten}` : ""} trang_thai={chiTietTinhThanh?.trang_thai} />}
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width="60vw"
        // styles={{
        //   body: {
        //     height: "60vh",
        //   },
        // }}
        footer={null}
        className="modal-ct-ngan-sach-ho-tro [&_.ant-space]:w-full">
        {renderTable()}
      </Modal>
      <ModalThemNganSachHoTroNgayAD ref={refModalThemNganSachHoTroNgayAD} />
    </Flex>
  );
});
ModalChiTietNganSachHoTroComponent.displayName = "ModalChiTietNganSachHoTroComponent";
export const ModalChiTietNganSachHoTro = memo(ModalChiTietNganSachHoTroComponent, isEqual);
